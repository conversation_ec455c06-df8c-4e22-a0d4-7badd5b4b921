// ==UserScript==
// @name         Emby 媒体库批量检查器
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  批量检查媒体是否存在于 Emby 服务器中，支持 Provider ID 和模糊搜索
// <AUTHOR>
// @match        *://*/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 配置管理
    class ConfigManager {
        static get(key, defaultValue = '') {
            return GM_getValue(key, defaultValue);
        }

        static set(key, value) {
            GM_setValue(key, value);
        }

        static getEmbyConfig() {
            return {
                host: this.get('emby_host', ''),
                apiKey: this.get('emby_api_key', ''),
                position: JSON.parse(this.get('fab_position', '{"x": 20, "y": 20}'))
            };
        }

        static saveEmbyConfig(host, apiKey) {
            this.set('emby_host', host);
            this.set('emby_api_key', apiKey);
        }

        static savePosition(x, y) {
            this.set('fab_position', JSON.stringify({x, y}));
        }
    }

    // Emby API 客户端
    class EmbyClient {
        constructor(host, apiKey) {
            this.host = host.replace(/\/$/, '');
            this.apiKey = apiKey;
        }

        async searchMedia(query, type = 'Movie,Series') {
            const url = `${this.host}/emby/Items?api_key=${this.apiKey}&searchTerm=${encodeURIComponent(query)}&IncludeItemTypes=${type}&Recursive=true&Fields=ProviderIds,Path,ProductionYear`;
            
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: 'GET',
                    url: url,
                    onload: (response) => {
                        try {
                            const data = JSON.parse(response.responseText);
                            resolve(data.Items || []);
                        } catch (e) {
                            reject(new Error('解析响应失败'));
                        }
                    },
                    onerror: () => reject(new Error('网络请求失败')),
                    ontimeout: () => reject(new Error('请求超时'))
                });
            });
        }

        async searchByProviderId(providerId, providerType) {
            const url = `${this.host}/emby/Items?api_key=${this.apiKey}&AnyProviderIdEquals=${providerType}.${providerId}&Recursive=true&Fields=ProviderIds,Path,ProductionYear`;
            
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: 'GET',
                    url: url,
                    onload: (response) => {
                        try {
                            const data = JSON.parse(response.responseText);
                            resolve(data.Items || []);
                        } catch (e) {
                            reject(new Error('解析响应失败'));
                        }
                    },
                    onerror: () => reject(new Error('网络请求失败')),
                    ontimeout: () => reject(new Error('请求超时'))
                });
            });
        }

        getItemUrl(itemId) {
            return `${this.host}/web/index.html#!/item?id=${itemId}`;
        }
    }

    // 媒体解析器
    class MediaParser {
        static parseMediaLine(line) {
            line = line.trim();
            if (!line) return null;

            // 尝试解析 Provider ID
            const tmdbMatch = line.match(/tmdb[:\s]*(\d+)/i);
            const imdbMatch = line.match(/imdb[:\s]*(tt\d+)/i);
            
            if (tmdbMatch) {
                return {
                    original: line,
                    providerId: tmdbMatch[1],
                    providerType: 'Tmdb',
                    title: line.replace(/tmdb[:\s]*\d+/i, '').trim()
                };
            }
            
            if (imdbMatch) {
                return {
                    original: line,
                    providerId: imdbMatch[1],
                    providerType: 'Imdb',
                    title: line.replace(/imdb[:\s]*tt\d+/i, '').trim()
                };
            }

            // 尝试解析标题和年份
            const yearMatch = line.match(/^(.+?)\s*[\(\[]?(\d{4})[\)\]]?\s*$/);
            if (yearMatch) {
                return {
                    original: line,
                    title: yearMatch[1].trim(),
                    year: parseInt(yearMatch[2])
                };
            }

            return {
                original: line,
                title: line
            };
        }
    }

    // 批量检查器
    class BatchChecker {
        constructor(embyClient) {
            this.embyClient = embyClient;
            this.results = [];
            this.onProgress = null;
            this.onComplete = null;
        }

        async checkMediaList(mediaList, concurrency = 3) {
            this.results = [];
            const total = mediaList.length;
            let completed = 0;

            const semaphore = new Array(concurrency).fill(null);
            
            const processItem = async (mediaInfo, index) => {
                try {
                    let found = false;
                    let embyItem = null;

                    // 优先使用 Provider ID 搜索
                    if (mediaInfo.providerId && mediaInfo.providerType) {
                        const items = await this.embyClient.searchByProviderId(mediaInfo.providerId, mediaInfo.providerType);
                        if (items.length > 0) {
                            found = true;
                            embyItem = items[0];
                        }
                    }

                    // 如果没找到，使用标题搜索
                    if (!found && mediaInfo.title) {
                        const items = await this.embyClient.searchMedia(mediaInfo.title);
                        if (items.length > 0) {
                            // 如果有年份，尝试匹配年份
                            if (mediaInfo.year) {
                                const yearMatch = items.find(item => item.ProductionYear === mediaInfo.year);
                                if (yearMatch) {
                                    embyItem = yearMatch;
                                    found = true;
                                } else if (items.length > 0) {
                                    embyItem = items[0];
                                    found = true;
                                }
                            } else {
                                embyItem = items[0];
                                found = true;
                            }
                        }
                    }

                    this.results[index] = {
                        ...mediaInfo,
                        found,
                        embyItem,
                        status: found ? 'found' : 'not_found'
                    };

                } catch (error) {
                    this.results[index] = {
                        ...mediaInfo,
                        found: false,
                        error: error.message,
                        status: 'error'
                    };
                }

                completed++;
                if (this.onProgress) {
                    this.onProgress(completed, total);
                }
            };

            // 并发处理
            const promises = mediaList.map(async (mediaInfo, index) => {
                // 等待信号量
                await new Promise(resolve => {
                    const check = () => {
                        const freeIndex = semaphore.findIndex(slot => slot === null);
                        if (freeIndex !== -1) {
                            semaphore[freeIndex] = index;
                            resolve();
                        } else {
                            setTimeout(check, 100);
                        }
                    };
                    check();
                });

                await processItem(mediaInfo, index);
                
                // 释放信号量
                const slotIndex = semaphore.findIndex(slot => slot === index);
                if (slotIndex !== -1) {
                    semaphore[slotIndex] = null;
                }
            });

            await Promise.all(promises);
            
            if (this.onComplete) {
                this.onComplete(this.results);
            }

            return this.results;
        }
    }

    // UI 管理器
    class UIManager {
        constructor() {
            console.log('[Emby Checker] UIManager 构造函数开始');
            this.isExpanded = false;
            this.isDragging = false;
            this.dragOffset = { x: 0, y: 0 };
            this.config = ConfigManager.getEmbyConfig();
            this.embyClient = null;
            this.batchChecker = null;

            console.log('[Emby Checker] 配置信息:', this.config);

            try {
                console.log('[Emby Checker] 创建样式...');
                this.createStyles();

                console.log('[Emby Checker] 创建 FAB...');
                this.createFAB();

                console.log('[Emby Checker] 创建面板...');
                this.createPanel();

                console.log('[Emby Checker] 设置事件监听器...');
                this.setupEventListeners();

                console.log('[Emby Checker] 更新 Emby 客户端...');
                this.updateEmbyClient();

                console.log('[Emby Checker] UIManager 初始化完成');
            } catch (error) {
                console.error('[Emby Checker] UIManager 初始化失败:', error);
                throw error;
            }
        }

        createStyles() {
            const style = document.createElement('style');
            style.textContent = `
                .emby-checker-fab {
                    position: fixed;
                    width: 56px;
                    height: 56px;
                    border-radius: 50%;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    cursor: pointer;
                    z-index: 10000;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 24px;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    user-select: none;
                }

                .emby-checker-fab:hover {
                    transform: scale(1.1);
                    box-shadow: 0 6px 25px rgba(0,0,0,0.4);
                }

                .emby-checker-fab.checking {
                    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                    animation: pulse 2s infinite;
                }

                .emby-checker-fab.completed {
                    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                }

                @keyframes pulse {
                    0%, 100% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                }

                .emby-checker-panel {
                    position: fixed;
                    width: 380px;
                    max-height: 600px;
                    background: rgba(255, 255, 255, 0.95);
                    backdrop-filter: blur(20px);
                    border-radius: 16px;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                    border: 1px solid rgba(255,255,255,0.2);
                    z-index: 10001;
                    display: none;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .emby-checker-panel.show {
                    display: block;
                    animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                @keyframes slideIn {
                    from {
                        opacity: 0;
                        transform: scale(0.9) translateY(-20px);
                    }
                    to {
                        opacity: 1;
                        transform: scale(1) translateY(0);
                    }
                }

                .emby-panel-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 16px 20px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    cursor: move;
                }

                .emby-panel-title {
                    font-size: 16px;
                    font-weight: 600;
                    margin: 0;
                }

                .emby-panel-close {
                    background: none;
                    border: none;
                    color: white;
                    font-size: 20px;
                    cursor: pointer;
                    padding: 4px;
                    border-radius: 4px;
                    transition: background-color 0.2s;
                }

                .emby-panel-close:hover {
                    background-color: rgba(255,255,255,0.2);
                }

                .emby-panel-content {
                    padding: 20px;
                    max-height: 520px;
                    overflow-y: auto;
                }

                .emby-config-section {
                    margin-bottom: 20px;
                    padding-bottom: 20px;
                    border-bottom: 1px solid rgba(0,0,0,0.1);
                }

                .emby-input-group {
                    margin-bottom: 12px;
                }

                .emby-input-group label {
                    display: block;
                    margin-bottom: 4px;
                    font-size: 14px;
                    font-weight: 500;
                    color: #333;
                }

                .emby-input {
                    width: 100%;
                    padding: 10px 12px;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    font-size: 14px;
                    transition: border-color 0.2s;
                    box-sizing: border-box;
                }

                .emby-input:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .emby-textarea {
                    width: 100%;
                    min-height: 120px;
                    padding: 12px;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    font-size: 14px;
                    resize: vertical;
                    font-family: monospace;
                    box-sizing: border-box;
                }

                .emby-btn {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s;
                    margin-right: 8px;
                    margin-bottom: 8px;
                }

                .emby-btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
                }

                .emby-btn:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                    transform: none;
                }

                .emby-btn-secondary {
                    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                }

                .emby-progress {
                    margin: 16px 0;
                    padding: 12px;
                    background: rgba(102, 126, 234, 0.1);
                    border-radius: 8px;
                    text-align: center;
                    font-size: 14px;
                    color: #667eea;
                    display: none;
                }

                .emby-progress.show {
                    display: block;
                }

                .emby-results {
                    margin-top: 16px;
                }

                .emby-result-item {
                    padding: 12px;
                    margin-bottom: 8px;
                    border-radius: 8px;
                    font-size: 14px;
                    border-left: 4px solid;
                }

                .emby-result-item.found {
                    background: rgba(76, 175, 80, 0.1);
                    border-left-color: #4caf50;
                }

                .emby-result-item.not-found {
                    background: rgba(255, 152, 0, 0.1);
                    border-left-color: #ff9800;
                }

                .emby-result-item.error {
                    background: rgba(244, 67, 54, 0.1);
                    border-left-color: #f44336;
                }

                .emby-result-title {
                    font-weight: 600;
                    margin-bottom: 4px;
                }

                .emby-result-info {
                    font-size: 12px;
                    color: #666;
                    margin-bottom: 4px;
                }

                .emby-result-link {
                    color: #667eea;
                    text-decoration: none;
                    font-size: 12px;
                }

                .emby-result-link:hover {
                    text-decoration: underline;
                }

                @media (max-width: 480px) {
                    .emby-checker-panel {
                        width: calc(100vw - 40px);
                        max-width: 380px;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        createFAB() {
            console.log('[Emby Checker] 开始创建 FAB 按钮');
            this.fab = document.createElement('button');
            this.fab.className = 'emby-checker-fab';
            this.fab.innerHTML = '🎬';
            this.fab.title = 'Emby 媒体库批量检查器';

            // 设置初始位置
            this.fab.style.left = this.config.position.x + 'px';
            this.fab.style.top = this.config.position.y + 'px';

            console.log('[Emby Checker] FAB 位置:', this.config.position);
            console.log('[Emby Checker] 添加 FAB 到 body');

            if (!document.body) {
                console.error('[Emby Checker] document.body 不存在！');
                return;
            }

            document.body.appendChild(this.fab);
            console.log('[Emby Checker] FAB 创建完成，元素:', this.fab);

            // 验证 FAB 是否真的添加到了页面中
            setTimeout(() => {
                const fabInDom = document.querySelector('.emby-checker-fab');
                console.log('[Emby Checker] FAB 在 DOM 中:', !!fabInDom);
                if (fabInDom) {
                    console.log('[Emby Checker] FAB 样式:', window.getComputedStyle(fabInDom));
                }
            }, 100);
        }

        createPanel() {
            this.panel = document.createElement('div');
            this.panel.className = 'emby-checker-panel';
            this.panel.innerHTML = `
                <div class="emby-panel-header">
                    <h3 class="emby-panel-title">Emby 媒体库检查器</h3>
                    <button class="emby-panel-close">×</button>
                </div>
                <div class="emby-panel-content">
                    <div class="emby-config-section">
                        <div class="emby-input-group">
                            <label for="emby-host">Emby 服务器地址:</label>
                            <input type="text" id="emby-host" class="emby-input" placeholder="http://localhost:8096" value="${this.config.host}">
                        </div>
                        <div class="emby-input-group">
                            <label for="emby-api-key">API Key:</label>
                            <input type="password" id="emby-api-key" class="emby-input" placeholder="您的 Emby API Key" value="${this.config.apiKey}">
                        </div>
                        <button id="save-config" class="emby-btn">保存配置</button>
                    </div>

                    <div class="emby-input-group">
                        <label for="media-list">媒体列表 (每行一个):</label>
                        <textarea id="media-list" class="emby-textarea" placeholder="输入媒体名称，支持格式：&#10;电影名称 (2023)&#10;TMDB: 12345&#10;IMDB: tt1234567&#10;或直接输入标题"></textarea>
                    </div>

                    <div>
                        <button id="start-check" class="emby-btn">开始检查</button>
                        <button id="clear-results" class="emby-btn emby-btn-secondary">清空结果</button>
                    </div>

                    <div id="progress" class="emby-progress"></div>
                    <div id="results" class="emby-results"></div>
                </div>
            `;

            document.body.appendChild(this.panel);
        }

        setupEventListeners() {
            // FAB 点击事件
            this.fab.addEventListener('click', (e) => {
                e.stopPropagation();
                this.togglePanel();
            });

            // 面板关闭按钮
            this.panel.querySelector('.emby-panel-close').addEventListener('click', () => {
                this.hidePanel();
            });

            // 保存配置
            this.panel.querySelector('#save-config').addEventListener('click', () => {
                this.saveConfig();
            });

            // 开始检查
            this.panel.querySelector('#start-check').addEventListener('click', () => {
                this.startCheck();
            });

            // 清空结果
            this.panel.querySelector('#clear-results').addEventListener('click', () => {
                this.clearResults();
            });

            // 拖拽功能
            this.setupDragFunctionality();

            // 点击外部关闭面板
            document.addEventListener('click', (e) => {
                if (this.isExpanded && !this.panel.contains(e.target) && !this.fab.contains(e.target)) {
                    this.hidePanel();
                }
            });
        }

        setupDragFunctionality() {
            let dragTarget = null;

            const startDrag = (e, target) => {
                this.isDragging = true;
                dragTarget = target;
                const rect = target.getBoundingClientRect();
                this.dragOffset.x = e.clientX - rect.left;
                this.dragOffset.y = e.clientY - rect.top;
                document.body.style.userSelect = 'none';
                e.preventDefault();
            };

            const drag = (e) => {
                if (!this.isDragging || !dragTarget) return;

                const x = Math.max(0, Math.min(window.innerWidth - dragTarget.offsetWidth, e.clientX - this.dragOffset.x));
                const y = Math.max(0, Math.min(window.innerHeight - dragTarget.offsetHeight, e.clientY - this.dragOffset.y));

                dragTarget.style.left = x + 'px';
                dragTarget.style.top = y + 'px';
            };

            const endDrag = () => {
                if (this.isDragging && dragTarget) {
                    const rect = dragTarget.getBoundingClientRect();
                    ConfigManager.savePosition(rect.left, rect.top);
                }
                this.isDragging = false;
                dragTarget = null;
                document.body.style.userSelect = '';
            };

            // FAB 拖拽
            this.fab.addEventListener('mousedown', (e) => startDrag(e, this.fab));

            // 面板头部拖拽
            this.panel.querySelector('.emby-panel-header').addEventListener('mousedown', (e) => {
                if (e.target.classList.contains('emby-panel-close')) return;
                startDrag(e, this.panel);
            });

            // 全局鼠标事件
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', endDrag);
        }

        togglePanel() {
            if (this.isExpanded) {
                this.hidePanel();
            } else {
                this.showPanel();
            }
        }

        showPanel() {
            this.isExpanded = true;
            this.panel.classList.add('show');

            // 调整面板位置，确保在屏幕内
            const fabRect = this.fab.getBoundingClientRect();
            let panelX = fabRect.right + 10;
            let panelY = fabRect.top;

            if (panelX + 380 > window.innerWidth) {
                panelX = fabRect.left - 390;
            }
            if (panelY + 600 > window.innerHeight) {
                panelY = window.innerHeight - 600;
            }
            if (panelX < 0) panelX = 10;
            if (panelY < 0) panelY = 10;

            this.panel.style.left = panelX + 'px';
            this.panel.style.top = panelY + 'px';
        }

        hidePanel() {
            this.isExpanded = false;
            this.panel.classList.remove('show');
        }

        updateEmbyClient() {
            if (this.config.host && this.config.apiKey) {
                this.embyClient = new EmbyClient(this.config.host, this.config.apiKey);
                this.batchChecker = new BatchChecker(this.embyClient);

                // 设置进度回调
                this.batchChecker.onProgress = (completed, total) => {
                    this.updateProgress(completed, total);
                };

                this.batchChecker.onComplete = (results) => {
                    this.displayResults(results);
                    this.fab.className = 'emby-checker-fab completed';
                    setTimeout(() => {
                        this.fab.className = 'emby-checker-fab';
                    }, 3000);
                };
            }
        }

        saveConfig() {
            const host = this.panel.querySelector('#emby-host').value.trim();
            const apiKey = this.panel.querySelector('#emby-api-key').value.trim();

            if (!host || !apiKey) {
                alert('请填写完整的服务器地址和 API Key');
                return;
            }

            ConfigManager.saveEmbyConfig(host, apiKey);
            this.config.host = host;
            this.config.apiKey = apiKey;
            this.updateEmbyClient();

            alert('配置已保存！');
        }

        async startCheck() {
            if (!this.embyClient) {
                alert('请先配置 Emby 服务器信息');
                return;
            }

            const mediaListText = this.panel.querySelector('#media-list').value.trim();
            if (!mediaListText) {
                alert('请输入要检查的媒体列表');
                return;
            }

            const lines = mediaListText.split('\n').filter(line => line.trim());
            const mediaList = lines.map(line => MediaParser.parseMediaLine(line)).filter(item => item);

            if (mediaList.length === 0) {
                alert('没有找到有效的媒体条目');
                return;
            }

            // 更新 UI 状态
            this.fab.className = 'emby-checker-fab checking';
            this.panel.querySelector('#start-check').disabled = true;
            this.showProgress(0, mediaList.length);
            this.clearResults();

            try {
                await this.batchChecker.checkMediaList(mediaList);
            } catch (error) {
                alert('检查过程中发生错误: ' + error.message);
                this.fab.className = 'emby-checker-fab';
            } finally {
                this.panel.querySelector('#start-check').disabled = false;
                this.hideProgress();
            }
        }

        updateProgress(completed, total) {
            const progressElement = this.panel.querySelector('#progress');
            progressElement.textContent = `正在检查... ${completed}/${total} (${Math.round(completed/total*100)}%)`;
        }

        showProgress(completed, total) {
            const progressElement = this.panel.querySelector('#progress');
            progressElement.classList.add('show');
            this.updateProgress(completed, total);
        }

        hideProgress() {
            const progressElement = this.panel.querySelector('#progress');
            progressElement.classList.remove('show');
        }

        displayResults(results) {
            const resultsContainer = this.panel.querySelector('#results');
            resultsContainer.innerHTML = '';

            if (results.length === 0) {
                resultsContainer.innerHTML = '<p style="text-align: center; color: #666;">没有结果</p>';
                return;
            }

            // 统计信息
            const found = results.filter(r => r.status === 'found').length;
            const notFound = results.filter(r => r.status === 'not_found').length;
            const errors = results.filter(r => r.status === 'error').length;

            const summary = document.createElement('div');
            summary.style.cssText = 'margin-bottom: 16px; padding: 12px; background: rgba(102, 126, 234, 0.1); border-radius: 8px; font-size: 14px;';
            summary.innerHTML = `
                <strong>检查完成:</strong>
                <span style="color: #4caf50;">找到 ${found}</span> |
                <span style="color: #ff9800;">未找到 ${notFound}</span> |
                <span style="color: #f44336;">错误 ${errors}</span>
            `;
            resultsContainer.appendChild(summary);

            // 结果列表
            results.forEach(result => {
                const item = document.createElement('div');
                item.className = `emby-result-item ${result.status}`;

                let content = `<div class="emby-result-title">${result.original}</div>`;

                if (result.status === 'found' && result.embyItem) {
                    content += `<div class="emby-result-info">✓ 在 Emby 中找到: ${result.embyItem.Name}`;
                    if (result.embyItem.ProductionYear) {
                        content += ` (${result.embyItem.ProductionYear})`;
                    }
                    content += `</div>`;

                    if (result.embyItem.Path) {
                        content += `<div class="emby-result-info">路径: ${result.embyItem.Path}</div>`;
                    }

                    const itemUrl = this.embyClient.getItemUrl(result.embyItem.Id);
                    content += `<a href="${itemUrl}" target="_blank" class="emby-result-link">在 Emby 中查看</a>`;

                } else if (result.status === 'not_found') {
                    content += `<div class="emby-result-info">✗ 未在 Emby 中找到</div>`;
                } else if (result.status === 'error') {
                    content += `<div class="emby-result-info">⚠ 查询错误: ${result.error}</div>`;
                }

                // 添加复制原始行的功能
                const copyBtn = document.createElement('button');
                copyBtn.textContent = '复制';
                copyBtn.style.cssText = 'float: right; font-size: 12px; padding: 2px 8px; background: #667eea; color: white; border: none; border-radius: 4px; cursor: pointer;';
                copyBtn.onclick = () => {
                    navigator.clipboard.writeText(result.original).then(() => {
                        copyBtn.textContent = '已复制';
                        setTimeout(() => copyBtn.textContent = '复制', 1000);
                    });
                };

                item.innerHTML = content;
                item.appendChild(copyBtn);
                resultsContainer.appendChild(item);
            });
        }

        clearResults() {
            const resultsContainer = this.panel.querySelector('#results');
            resultsContainer.innerHTML = '';
        }
    }

    // 调试函数
    function debugLog(message) {
        console.log('[Emby Checker Debug]', message);
    }

    // 等待页面加载完成后初始化
    function initializeScript() {
        try {
            debugLog('开始初始化脚本...');
            debugLog('Document ready state:', document.readyState);
            debugLog('Body exists:', !!document.body);

            if (!document.body) {
                debugLog('Body 不存在，等待 body 加载...');
                setTimeout(initializeScript, 100);
                return;
            }

            debugLog('创建 UIManager...');
            const uiManager = new UIManager();
            debugLog('UIManager 创建成功');

            // 添加一个测试按钮来验证脚本是否正常工作
            setTimeout(() => {
                const testBtn = document.createElement('div');
                testBtn.style.cssText = `
                    position: fixed;
                    top: 10px;
                    right: 10px;
                    background: red;
                    color: white;
                    padding: 10px;
                    z-index: 99999;
                    cursor: pointer;
                    border-radius: 5px;
                    font-size: 12px;
                `;
                testBtn.textContent = 'Emby脚本已加载';
                testBtn.onclick = () => {
                    alert('Emby 检查器脚本正在运行！\n如果看不到悬浮按钮，请检查浏览器控制台的错误信息。');
                };
                document.body.appendChild(testBtn);

                // 5秒后自动移除测试按钮
                setTimeout(() => {
                    if (testBtn.parentNode) {
                        testBtn.parentNode.removeChild(testBtn);
                    }
                }, 5000);
            }, 1000);

        } catch (error) {
            debugLog('初始化失败:', error);
            console.error('[Emby Checker Error]', error);

            // 显示错误信息
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
                position: fixed;
                top: 50px;
                right: 10px;
                background: #ff4444;
                color: white;
                padding: 15px;
                z-index: 99999;
                border-radius: 5px;
                max-width: 300px;
                font-size: 12px;
            `;
            errorDiv.innerHTML = `
                <strong>Emby 脚本错误:</strong><br>
                ${error.message}<br>
                <button onclick="this.parentNode.remove()" style="margin-top: 10px; background: white; color: #ff4444; border: none; padding: 5px; border-radius: 3px; cursor: pointer;">关闭</button>
            `;
            document.body.appendChild(errorDiv);
        }
    }

    if (document.readyState === 'loading') {
        debugLog('文档正在加载，等待 DOMContentLoaded...');
        document.addEventListener('DOMContentLoaded', initializeScript);
    } else {
        debugLog('文档已加载完成，立即初始化...');
        initializeScript();
    }

})();
