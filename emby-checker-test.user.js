// ==UserScript==
// @name         Emby 检查器测试版
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  测试版本 - 检查脚本是否能正常加载
// <AUTHOR>
// @match        *://*/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('[Emby Test] 脚本开始执行');
    
    function createTestButton() {
        console.log('[Emby Test] 创建测试按钮');
        
        // 创建一个简单的测试按钮
        const testBtn = document.createElement('div');
        testBtn.id = 'emby-test-button';
        testBtn.innerHTML = '🎬 测试';
        testBtn.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 99999;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            font-size: 12px;
            text-align: center;
            line-height: 1.2;
        `;
        
        testBtn.onclick = function() {
            alert('测试按钮点击成功！\n这说明油猴脚本可以正常运行。\n\n如果你看到这个消息，说明问题可能在主脚本的某个部分。');
        };
        
        // 添加悬停效果
        testBtn.onmouseenter = function() {
            this.style.transform = 'scale(1.1)';
        };
        
        testBtn.onmouseleave = function() {
            this.style.transform = 'scale(1)';
        };
        
        document.body.appendChild(testBtn);
        console.log('[Emby Test] 测试按钮已添加到页面');
        
        // 5秒后显示提示
        setTimeout(() => {
            if (document.getElementById('emby-test-button')) {
                console.log('[Emby Test] 测试按钮仍在页面中，脚本运行正常');
            }
        }, 5000);
    }
    
    function init() {
        console.log('[Emby Test] 初始化函数执行');
        console.log('[Emby Test] Document ready state:', document.readyState);
        console.log('[Emby Test] Body exists:', !!document.body);
        
        if (!document.body) {
            console.log('[Emby Test] Body 不存在，延迟执行');
            setTimeout(init, 100);
            return;
        }
        
        createTestButton();
    }
    
    // 立即执行或等待 DOM 加载
    if (document.readyState === 'loading') {
        console.log('[Emby Test] 等待 DOM 加载完成');
        document.addEventListener('DOMContentLoaded', init);
    } else {
        console.log('[Emby Test] DOM 已加载，立即执行');
        init();
    }
    
    console.log('[Emby Test] 脚本设置完成');
})();
